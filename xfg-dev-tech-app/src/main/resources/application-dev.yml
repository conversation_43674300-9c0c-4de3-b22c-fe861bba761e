server:
  port: 8090

spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: postgres
    url: ******************************************************
    type: com.zaxxer.hikari.HikariDataSource
    # hikari连接池配置
    hikari:
      #连接池名
      pool-name: HikariCP
      #最小空闲连接数
      minimum-idle: 5
      # 空闲连接存活最大时间，默认10分钟
      idle-timeout: 600000
      # 连接池最大连接数，默认是10
      maximum-pool-size: 10
      # 此属性控制从池返回的连接的默认自动提交行为,默认值：true
      auto-commit: true
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
      max-lifetime: 1800000
      # 数据库连接超时时间,默认30秒
      connection-timeout: 30000
      # 连接测试query
      connection-test-query: SELECT 1
  ai:
    ollama:
      base-url: http://*************:11434
      embedding:
        options:
          num-batch: 512
        model: nomic-embed-text
    openai:
      base-url: https://api.vveai.com
      api-key: sk-AVT3KUqNAp4J5lHy4cC364325bE54eAdAeAa33D2Fc66824e
      embedding-model: text-embedding-ada-002
    rag:
      embed: nomic-embed-text


# Redis
redis:
  sdk:
    config:
      host: *************
      port: 16379
      pool-size: 10
      min-idle-size: 5
      idle-timeout: 30000
      connect-timeout: 10000
      retry-attempts: 5
      retry-interval: 2000
      ping-interval: 60000
      keep-alive: true

logging:
  level:
    root: info
  config: classpath:logback-spring.xml
