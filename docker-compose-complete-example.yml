version: '3.8'

services:
  # Nginx 前端代理
  nginx:
    image: nginx:latest
    container_name: nginx-proxy
    restart: always
    ports:
      - '80:80'
      - '443:443'  # 如果需要HTTPS
    volumes:
      - ./nginx/html:/usr/share/nginx/html  # 前端静态文件
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf  # 主配置
      - ./nginx/conf/conf.d:/etc/nginx/conf.d  # 站点配置
      - ./nginx/logs:/var/log/nginx  # 日志目录
    depends_on:
      - ai-rag-knowledge-app
    networks:
      - app-network

  # SpringBoot 后端应用
  ai-rag-knowledge-app:
    image: fuzhengwei/ai-rag-knowledge-app:1.2
    container_name: ai-rag-knowledge-app
    restart: on-failure
    ports:
      - "8090:8090"
    environment:
      - TZ=PRC
      - SERVER_PORT=8090
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATASOURCE_URL=*************************************************
    depends_on:
      - vector_db
      - redis
    networks:
      - app-network

  # PostgreSQL 向量数据库
  vector_db:
    image: pgvector/pgvector:pg16
    container_name: vector_db
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=ai-rag-knowledge
    ports:
      - '15432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./pgvector/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # Redis 缓存
  redis:
    image: redis:6.2
    container_name: redis
    restart: always
    ports:
      - "16379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
