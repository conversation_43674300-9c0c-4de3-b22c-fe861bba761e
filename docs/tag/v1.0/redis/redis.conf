# Redis 配置文件
bind 0.0.0.0
port 6379

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置 - 禁用 RDB 快照以避免磁盘写入错误
save ""

# 禁用 stop-writes-on-bgsave-error 选项
stop-writes-on-bgsave-error no

# AOF 持久化配置（可选，如果需要持久化可以启用）
appendonly no
appendfsync everysec

# 日志配置
loglevel notice
logfile ""

# 网络配置
timeout 0
keepalive 300

# 客户端配置
maxclients 10000

# 安全配置
protected-mode no

# 其他配置
databases 16
tcp-backlog 511